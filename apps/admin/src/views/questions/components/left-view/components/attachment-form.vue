<script setup lang="ts">
import { useNaiveForm } from '@sa/hooks'
import FileUpload from '@sa/components/common/file-upload.vue'

defineOptions({
  name: 'AttachmentForm',
})

const modelInfo = defineModel('modelInfo', {
  type: Object as PropType<{
    FileUrls: string[]
    AdditionalRequirements: string
  }>,
  default: () => ({
    FileUrls: [],
    AdditionalRequirements: '',
  }),
})

// 表单引用和校验
const { formRef, validate } = useNaiveForm()

// 文件上传组件引用
const fileUploadRef = ref()

// 表单校验规则
const rules = {
  FileUrls: [
    {
      required: true,
      message: '请上传附件文件',
      trigger: ['blur', 'change'],
      validator: (_rule: any, value: any[]) => {
        if (!value || value.length === 0) {
          return new Error('请上传附件文件')
        }
        return true
      },
    },
  ],
}

// 文件变化处理
function handleFileChange(fileList: (string | string[])[]) {
  // 将 FileItem[] 转换为 string[]
  const urls: string[] = []
  fileList.forEach((item) => {
    if (typeof item === 'string') {
      urls.push(item)
    }
    else if (Array.isArray(item)) {
      urls.push(...item)
    }
  })
  modelInfo.value.FileUrls = urls
}

// 上传成功处理
function handleUploadSuccess(_file: string | string[]) {
  window.$message?.success('文件上传成功')
}

// 上传失败处理
function handleUploadError(error: any, _file: File) {
  console.error('文件上传失败:', error)
  window.$message?.error('文件上传失败')
}

// 暴露校验方法给父组件
defineExpose({
  validate,
})
</script>

<template>
  <div class="p-12px">
    <NForm ref="formRef" :model="modelInfo" :rules="rules">
      <!-- 出题范围标题 -->
      <div class="mb-16px flex items-center">
        <SvgIcon icon="mdi:file-document-outline" class="mr-8px text-20px text-blue-500" />
        <span class="text-14px text-[#333] font-500">出题范围</span>
      </div>

      <!-- 文件上传 -->
      <NFormItem path="FileUrls">
        <template #label>
          <span class="text-left text-14px text-[#464646] font-500">上传附件</span>
        </template>
        <FileUpload
          ref="fileUploadRef"
          v-model="modelInfo.FileUrls"
          :is-directory-dnd="true"
          :max="3"
          accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png"
          list-type="text"
          directory-dnd
          @change="handleFileChange"
          @upload-success="handleUploadSuccess"
          @upload-error="handleUploadError"
        >
          <div>
            <div class="mb-12px flex items-center justify-center gap-8px">
              <SvgIcon icon="mdi:cloud-upload" class="text-48px text-gray-400" />
              <NText class="text-16px">
                点击或者拖动文件到该区域来上传
              </NText>
            </div>

            <NText depth="3" class="mt-8px text-12px">
              最多上传 3 个文件
            </NText>
            <NText depth="3" class="text-12px">
              支持上传图片、Word、PDF 格式文件，单个文件大小限制 20MB 以内
            </NText>
            <NText depth="3" class="text-12px">
              文件解析需耗时，提交后请耐心等待，勿重复操作
            </NText>
          </div>
        </FileUpload>
      </NFormItem>

      <!-- 补充内容区域 -->
      <div class="border border-gray-200 rounded-8px bg-white">
        <!-- 标题栏 -->
        <div class="flex items-center justify-between p-16px">
          <span class="text-14px text-[#464646] font-500">补充内容（选填）</span>
        </div>

        <!-- 可折叠内容 -->
        <div class="overflow-hidden transition-all duration-300">
          <div class="border-t border-gray-200 p-16px">
            <NInput
              v-model:value="modelInfo.AdditionalRequirements"
              type="textarea"
              placeholder="您可以补充特殊的出题要求。&#10;示例如下：&#10;题目中需要包含&quot;折射&quot;&quot;反射&quot;这两个词汇"
              :rows="4"
              clearable
              maxlength="500"
              show-count
              class="w-full"
            />
          </div>
        </div>
      </div>
    </NForm>
  </div>
</template>
